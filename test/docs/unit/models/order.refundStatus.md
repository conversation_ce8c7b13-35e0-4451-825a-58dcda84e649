# Order Model refundStatus 单元测试文档

## 测试概述

本文档描述了 Order 模型中 `refundStatus` 字段的单元测试，验证该字段的默认值、枚举值和功能正确性。

## 测试文件

- **文件路径**: `test/unit/models/order.refundStatus.simple.test.js`
- **测试框架**: Jest
- **数据库**: MongoDB (通过 testcontainers)

## 测试用例

### 1. Schema 定义验证

#### 测试名称
`should verify refundStatus field exists in schema`

#### 测试目的
验证 `refundStatus` 字段在 Order schema 中正确定义

#### 验证内容
- 字段存在性
- 枚举值: ['NONE', 'PARTIAL', 'FULL']
- 默认值: 'NONE'

#### 测试结果
```
✅ refundStatus schema definition test passed
Enum values: [ 'NONE', 'PARTIAL', 'FULL' ]
Default value: NONE
```

### 2. 默认值验证

#### 测试名称
`should create order with minimal data and verify refundStatus default`

#### 测试目的
验证创建 Order 实例时 `refundStatus` 的默认值

#### 测试步骤
1. 创建包含所有必需字段的 Order 数据
2. 不显式设置 `refundStatus`
3. 创建 Order 实例
4. 验证默认值

#### 必需字段
```javascript
{
  orderId: 'SIMPLE-{timestamp}',
  orderAmount: 100.00,
  restaurantId: '507f1f77bcf86cd799439011',
  restaurantName: 'Test Restaurant',
  restaurantBrand: 'Test Brand',        // 必需
  restaurantBrandId: '507f1f77bcf86cd799439012', // 必需
  customerId: 'CUST-SIMPLE-001',
  customerPhone: '+1234567890',
  deliveryAddress: 'Simple Address',
  deliveryAddressId: 'ADDR-SIMPLE-001', // 必需
  items: [/* 简化的 item 结构 */],
  orderStatus: 'PENDING',
  paymentMethod: 'STRIPE',
  paymentStatus: 'PAID',
  paidAmount: 100.00
}
```

#### 验证结果
- `refundStatus`: 'NONE'
- `totalRefunded`: 0
- `refunds`: []

#### 测试结果
```
✅ Order created with correct refundStatus default
refundStatus: NONE
totalRefunded: 0
refunds length: 0
✅ Order saved successfully with refundStatus: NONE
```

### 3. 枚举值验证

#### 测试名称
`should verify refundStatus can be set to valid enum values`

#### 测试目的
验证 `refundStatus` 字段可以设置为所有有效的枚举值

#### 测试步骤
1. 验证所有枚举值存在于 schema 中
2. 确认默认值在有效枚举值中

#### 验证的枚举值
- 'NONE'
- 'PARTIAL'
- 'FULL'

#### 测试结果
```
✅ refundStatus enum validation passed
Valid values: [ 'NONE', 'PARTIAL', 'FULL' ]
Default value: NONE
```

### 4. 相关字段验证

#### 测试名称
`should verify Order model has all refund-related fields`

#### 测试目的
验证 Order 模型包含所有退款相关字段

#### 验证字段
- `refundStatus`: 存在且默认值为 'NONE'
- `totalRefunded`: 存在且默认值为 0
- `refunds`: 存在（数组字段）

#### 测试结果
```
✅ All refund-related fields verified
refundStatus default: NONE
totalRefunded default: 0
refunds field exists: true
```

### 5. 功能性验证

#### 测试名称
`should demonstrate refundStatus functionality without complex ObjectIds`

#### 测试目的
验证 `refundStatus` 字段的功能性，包括值的设置和修改

#### 测试步骤
1. 创建 Order 实例
2. 验证默认值 'NONE'
3. 设置为 'PARTIAL' 并验证
4. 设置为 'FULL' 并验证
5. 重置为 'NONE' 并验证

#### 测试结果
```
✅ refundStatus functionality test passed
All enum values work correctly
```

## 测试环境配置

### 数据库连接
- 使用 `testDatabase` helper 进行正确的数据库连接
- 支持 testcontainers 提供的 MongoDB 实例
- 自动清理测试数据

### 调试信息
测试包含详细的调试信息：
```
🔍 Debug Info:
mongoose: true
mongoose.Types: true
mongoose.Types.ObjectId: true
mongoose.connection.readyState: 0
mongoose.version: 6.13.0
✅ ObjectId creation successful
```

## 问题解决

### 常见问题：缺少必需字段

#### 问题描述
创建 Order 实例时出现 `Cannot read properties of null (reading 'ObjectId')` 错误

#### 根本原因
Order 模型有多个必需字段，如果缺少这些字段，mongoose 在处理 ObjectId 字段时会出错

#### 必需字段列表
1. `restaurantBrand` - 餐厅品牌名称
2. `deliveryAddressId` - 配送地址ID
3. 其他基本必需字段（orderId, orderAmount, restaurantId 等）

#### 解决方案
确保测试数据包含所有必需字段：
```javascript
const orderData = {
  // 基本必需字段
  orderId: 'TEST-ORDER-001',
  orderAmount: 100.00,
  restaurantId: '507f1f77bcf86cd799439011',
  restaurantName: 'Test Restaurant',
  customerId: 'CUST-001',
  deliveryAddress: 'Test Address',
  orderStatus: 'PENDING',
  paymentMethod: 'STRIPE',
  paymentStatus: 'PAID',
  paidAmount: 100.00,
  
  // 容易遗漏的必需字段
  restaurantBrand: 'Test Brand',           // 必需！
  restaurantBrandId: '507f1f77bcf86cd799439012', // 必需！
  deliveryAddressId: 'ADDR-001',          // 必需！
  
  // 简化的 items 结构
  items: [{
    title: 'Test Item',
    quantity: 1,
    variation: { title: 'Regular', price: 100.00 }
  }]
};
```

## 测试执行

### 运行命令
```bash
npm run test:unit -- --testPathPattern="order.refundStatus.simple.test.js" --verbose
```

### 预期结果
```
Test Suites: 1 passed, 1 total
Tests: 5 passed, 5 total
Snapshots: 0 total
Time: ~2.3s
```

### 成功标志
- 所有 5 个测试用例通过
- 无错误或警告
- 正确的默认值验证
- 完整的枚举值验证

## 最佳实践

1. **完整的必需字段**: 始终提供所有必需字段以避免 mongoose 错误
2. **使用 testDatabase helper**: 确保正确的数据库连接和清理
3. **详细的调试信息**: 在测试失败时提供足够的调试信息
4. **原子测试**: 每个测试用例验证一个特定的功能点
5. **清理策略**: 每个测试前清理数据库以确保测试独立性

## 相关文档

- [Order Model API 文档](../../../docs/api/models/order.md)
- [退款系统功能文档](../../../docs/Features/refund.md)
- [测试最佳实践](../TESTING_BEST_PRACTICES.md)
