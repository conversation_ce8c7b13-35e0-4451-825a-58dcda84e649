# 🎯 测试最佳实践

本文档定义了 Firespoon API 项目的测试最佳实践，确保测试的质量、可维护性和有效性。

## 📋 核心原则

### 1. 测试金字塔原则

```
        /\
       /  \
      / E2E \     <- 少量，慢，昂贵，但覆盖完整流程
     /______\
    /        \
   /Integration\ <- 中等数量，中等速度，测试组件交互
  /__________\
 /            \
/    Unit      \ <- 大量，快速，便宜，测试单个功能
/______________\
```

**比例建议**:
- 单元测试: 70%
- 集成测试: 20%
- E2E 测试: 10%

### 2. FIRST 原则

- **Fast** (快速): 测试应该快速运行
- **Independent** (独立): 测试之间不应相互依赖
- **Repeatable** (可重复): 在任何环境中都能重复运行
- **Self-Validating** (自验证): 测试结果应该是明确的 pass/fail
- **Timely** (及时): 测试应该及时编写

## 🔍 Mock 使用策略

### 何时使用 Mock

#### ✅ 应该 Mock 的场景

1. **外部服务调用**
```javascript
// ✅ Mock 外部 API
jest.mock('stripe', () => ({
  paymentIntents: {
    create: jest.fn().mockResolvedValue({ id: 'pi_test' })
  }
}));
```

2. **文件系统操作**
```javascript
// ✅ Mock 文件操作
jest.mock('fs', () => ({
  readFile: jest.fn().mockResolvedValue('file content')
}));
```

3. **时间相关函数**
```javascript
// ✅ Mock 时间
jest.useFakeTimers();
jest.setSystemTime(new Date('2023-01-01'));
```

4. **随机数生成**
```javascript
// ✅ Mock 随机数
jest.spyOn(Math, 'random').mockReturnValue(0.5);
```

#### ❌ 不应该 Mock 的场景

1. **数据库操作** (在集成测试中)
```javascript
// ❌ 不要在集成测试中 mock 数据库
// 使用真实的测试数据库
const user = await User.create({ name: 'Test' });
```

2. **业务逻辑**
```javascript
// ❌ 不要 mock 被测试的业务逻辑
// 应该测试真实的逻辑
const result = calculateOrderTotal(items, tax);
```

3. **内部模块** (除非必要)
```javascript
// ❌ 避免 mock 内部模块
// 让真实的模块交互发生
const orderService = require('./orderService');
```

### Mock 最佳实践

#### 1. 使用有意义的 Mock 数据

```javascript
// ✅ 好的 Mock 数据
const mockUser = {
  id: 'user_123',
  email: '<EMAIL>',
  name: 'Test User',
  createdAt: '2023-01-01T00:00:00Z'
};

// ❌ 无意义的 Mock 数据
const mockUser = {
  id: 'abc',
  email: 'test',
  name: 'user'
};
```

#### 2. 重置 Mock 状态

```javascript
describe('Service Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks(); // 清理 mock 调用历史
  });

  afterAll(() => {
    jest.restoreAllMocks(); // 恢复原始实现
  });
});
```

#### 3. 验证 Mock 调用

```javascript
test('should call external service with correct parameters', async () => {
  const mockSendEmail = jest.fn().mockResolvedValue({ success: true });
  emailService.sendEmail = mockSendEmail;

  await notificationService.sendWelcomeEmail('<EMAIL>');

  expect(mockSendEmail).toHaveBeenCalledWith({
    to: '<EMAIL>',
    subject: 'Welcome',
    template: 'welcome'
  });
  expect(mockSendEmail).toHaveBeenCalledTimes(1);
});
```

## 🏗️ 测试结构

### 1. 描述性测试名称

```javascript
// ✅ 好的测试名称
describe('OrderService', () => {
  describe('createOrder', () => {
    test('should create order when valid data is provided', () => {});
    test('should throw ValidationError when required fields are missing', () => {});
    test('should calculate total price including tax and delivery charges', () => {});
  });
});

// ❌ 不好的测试名称
describe('OrderService', () => {
  test('test1', () => {});
  test('error case', () => {});
  test('calculation', () => {});
});
```

### 2. AAA 模式 (Arrange-Act-Assert)

```javascript
test('should calculate order total correctly', () => {
  // Arrange - 准备测试数据
  const items = [
    { price: 10.00, quantity: 2 },
    { price: 5.00, quantity: 1 }
  ];
  const taxRate = 0.08;
  const deliveryFee = 3.00;

  // Act - 执行被测试的操作
  const total = calculateOrderTotal(items, taxRate, deliveryFee);

  // Assert - 验证结果
  expect(total).toBe(30.00); // (10*2 + 5*1) * 1.08 + 3.00
});
```

### 3. 单一职责原则

```javascript
// ✅ 每个测试只验证一个行为
test('should validate email format', () => {
  expect(validateEmail('<EMAIL>')).toBe(true);
});

test('should reject invalid email format', () => {
  expect(validateEmail('invalid-email')).toBe(false);
});

// ❌ 一个测试验证多个行为
test('should validate email', () => {
  expect(validateEmail('<EMAIL>')).toBe(true);
  expect(validateEmail('invalid-email')).toBe(false);
  expect(validateEmail('')).toBe(false);
});
```

## 📊 数据管理

### 1. 使用测试工厂

```javascript
// ✅ 使用工厂创建测试数据
const { createUser, createOrder } = require('../factories');

test('should process order', async () => {
  const user = await createUser({ email: '<EMAIL>' });
  const order = await createOrder({ userId: user._id });
  
  const result = await processOrder(order._id);
  expect(result.status).toBe('processed');
});
```

### 2. 数据隔离

```javascript
describe('User Tests', () => {
  beforeEach(async () => {
    // 每个测试前清理数据
    await clearTestDB();
  });

  test('should create user', async () => {
    // 测试在干净的数据库状态下运行
    const user = await createUser({ name: 'Test' });
    expect(user.name).toBe('Test');
  });
});
```

### 3. 测试数据的真实性

```javascript
// ✅ 使用真实的数据格式
const testOrder = {
  orderId: 'ORD-2023-001',
  customerEmail: '<EMAIL>',
  items: [
    {
      foodId: 'food_burger_001',
      name: 'Classic Burger',
      price: 12.99,
      quantity: 2
    }
  ],
  deliveryAddress: '123 Main St, City, State 12345',
  orderAmount: 28.98,
  createdAt: new Date('2023-01-01T12:00:00Z')
};

// ❌ 使用简化的测试数据
const testOrder = {
  id: 1,
  email: 'test',
  items: [{ id: 1, price: 10 }]
};
```

## 🔄 异步测试

### 1. 正确处理 Promise

```javascript
// ✅ 使用 async/await
test('should create user asynchronously', async () => {
  const user = await createUser({ name: 'Test' });
  expect(user.name).toBe('Test');
});

// ✅ 返回 Promise
test('should create user asynchronously', () => {
  return createUser({ name: 'Test' }).then(user => {
    expect(user.name).toBe('Test');
  });
});

// ❌ 不等待异步操作
test('should create user asynchronously', () => {
  createUser({ name: 'Test' }).then(user => {
    expect(user.name).toBe('Test'); // 可能不会执行
  });
});
```

### 2. 测试错误情况

```javascript
// ✅ 测试异步错误
test('should throw error for invalid data', async () => {
  await expect(createUser({})).rejects.toThrow('Name is required');
});

// ✅ 测试 HTTP 错误
test('should return 400 for invalid request', async () => {
  const response = await request(app)
    .post('/api/users')
    .send({})
    .expect(400);

  expect(response.body.error).toContain('Name is required');
});
```

## 🎭 集成测试策略

### 1. 最小化 Mock

```javascript
// ✅ 集成测试中最小化 mock
describe('Order API Integration', () => {
  test('should create order with real database', async () => {
    // 使用真实数据库，不 mock 数据层
    const customer = await Customer.create({ name: 'Test' });
    
    const response = await request(app)
      .post('/api/orders')
      .send({
        customerId: customer._id,
        items: [{ foodId: 'food_1', quantity: 1 }]
      })
      .expect(201);

    // 验证数据库中的实际数据
    const order = await Order.findById(response.body.order._id);
    expect(order.customerId.toString()).toBe(customer._id.toString());
  });
});
```

### 2. 测试真实的 API 交互

```javascript
// ✅ 使用真实的测试环境
describe('Stripe Integration', () => {
  test('should create payment intent with real Stripe API', async () => {
    // 需要 STRIPE_TEST_SECRET_KEY 环境变量
    if (!process.env.STRIPE_TEST_SECRET_KEY) {
      console.log('Skipping real Stripe test');
      return;
    }

    const response = await request(app)
      .post('/api/payments/stripe/create-intent')
      .send({
        amount: 2000,
        currency: 'usd'
      })
      .expect(200);

    expect(response.body.paymentIntent.id).toMatch(/^pi_/);
  });
});
```

## 🚀 性能测试

### 1. 测试响应时间

```javascript
test('should respond within acceptable time', async () => {
  const startTime = Date.now();
  
  await request(app)
    .get('/api/restaurants')
    .expect(200);
  
  const responseTime = Date.now() - startTime;
  expect(responseTime).toBeLessThan(1000); // 1秒内响应
});
```

### 2. 测试并发处理

```javascript
test('should handle concurrent requests', async () => {
  const requests = Array(10).fill().map(() =>
    request(app)
      .post('/api/orders')
      .send(validOrderData)
      .expect(201)
  );

  const responses = await Promise.all(requests);
  
  // 验证所有请求都成功
  responses.forEach(response => {
    expect(response.body.success).toBe(true);
  });
});
```

## 🔍 测试覆盖率

### 1. 关注有意义的覆盖率

```javascript
// ✅ 测试重要的业务逻辑
test('should apply discount correctly', () => {
  const order = { total: 100 };
  const discount = { type: 'percentage', value: 10 };
  
  const result = applyDiscount(order, discount);
  expect(result.total).toBe(90);
});

// ❌ 为了覆盖率而测试简单的 getter
test('should return user name', () => {
  const user = new User('John');
  expect(user.getName()).toBe('John'); // 没有实际价值
});
```

### 2. 忽略不重要的代码

```javascript
// 在 jest.config.js 中配置
module.exports = {
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/test/',
    '/config/',
    'index.js' // 简单的入口文件
  ]
};
```

## 🐛 错误处理测试

### 1. 测试边界条件

```javascript
describe('calculateDiscount', () => {
  test('should handle zero amount', () => {
    expect(calculateDiscount(0, 10)).toBe(0);
  });

  test('should handle negative amount', () => {
    expect(() => calculateDiscount(-10, 10)).toThrow('Amount cannot be negative');
  });

  test('should handle 100% discount', () => {
    expect(calculateDiscount(100, 100)).toBe(0);
  });

  test('should handle discount greater than 100%', () => {
    expect(() => calculateDiscount(100, 150)).toThrow('Discount cannot exceed 100%');
  });
});
```

### 2. 测试错误恢复

```javascript
test('should retry failed operations', async () => {
  const mockService = jest.fn()
    .mockRejectedValueOnce(new Error('Network error'))
    .mockRejectedValueOnce(new Error('Network error'))
    .mockResolvedValueOnce({ success: true });

  const result = await retryableOperation(mockService, { maxRetries: 3 });
  
  expect(result.success).toBe(true);
  expect(mockService).toHaveBeenCalledTimes(3);
});
```

## 📝 文档和维护

### 1. 测试文档

```javascript
/**
 * 测试订单创建流程
 * 
 * 场景：
 * 1. 客户选择商品
 * 2. 添加配送地址
 * 3. 选择支付方式
 * 4. 创建订单
 * 
 * 验证：
 * - 订单状态为 PENDING
 * - 总金额计算正确
 * - 发送确认邮件
 */
describe('Order Creation Flow', () => {
  // 测试实现
});
```

### 2. 定期维护

- **删除过时的测试**: 当功能被移除时
- **更新测试数据**: 保持与生产数据格式一致
- **重构重复代码**: 提取公共的测试逻辑
- **审查测试覆盖率**: 确保新功能有适当的测试

## 🎯 总结

遵循这些最佳实践将帮助你：

1. **编写可靠的测试**: 减少假阳性和假阴性
2. **提高开发效率**: 快速发现和修复问题
3. **增强代码质量**: 通过测试驱动更好的设计
4. **简化维护**: 易于理解和修改的测试代码
5. **建立信心**: 对代码变更有信心

记住：**好的测试不仅仅是为了覆盖率，而是为了确保代码的正确性和可靠性**。
