# 🧪 Firespoon API 测试指南

本文档提供了 Firespoon API 项目的完整测试指南，包括测试架构、最佳实践、运行方法和覆盖率分析。

## 📋 目录

- [测试架构](#测试架构)
- [测试类型](#测试类型)
- [运行测试](#运行测试)
- [测试覆盖率](#测试覆盖率)
- [编写新测试](#编写新测试)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🏗️ 测试架构

### 目录结构

```
test/
├── unit/                    # 单元测试
│   ├── whatsapp/           # WhatsApp 服务单元测试
│   │   ├── services/       # 服务层测试
│   │   └── utils/          # 工具函数测试
│   └── ...
├── integration/            # 集成测试
│   ├── graphql/           # GraphQL API 测试
│   ├── payment/           # 支付系统测试
│   ├── order/             # 订单管理测试
│   └── whatsapp/          # WhatsApp 集成测试
├── e2e/                   # 端到端测试
│   └── orderFlow.test.js  # 完整订单流程测试
├── factories/             # 测试数据工厂
├── helpers/               # 测试辅助工具
├── config/                # 测试配置
└── docs/                  # 测试文档
```

### 测试环境配置

项目使用 **Testcontainers** 来提供一致的测试环境：

- **MongoDB**: 真实的 MongoDB 7.0 容器
- **Redis**: 真实的 Redis 7.2.4 容器
- **隔离性**: 每个测试套件都有独立的数据库实例

## 🔬 测试类型

### 1. 单元测试 (Unit Tests)

**目的**: 测试单个函数或类的功能
**特点**: 快速、隔离、大量使用 mock
**位置**: `test/unit/`

```bash
# 运行所有单元测试
npm run test:unit

# 运行特定单元测试
npm test -- test/unit/whatsapp/services/whatsappService.test.js
```

### 2. 集成测试 (Integration Tests)

**目的**: 测试多个组件之间的交互
**特点**: 使用真实数据库，最少 mock
**位置**: `test/integration/`

```bash
# 运行所有集成测试
npm run test:integration

# 运行特定集成测试
npm test -- test/integration/order/orderManagement.test.js
```

### 3. 端到端测试 (E2E Tests)

**目的**: 测试完整的业务流程
**特点**: 模拟真实用户场景，无 mock
**位置**: `test/e2e/`

```bash
# 运行端到端测试
npm run test:e2e

# 运行特定 E2E 测试
npm test -- test/e2e/orderFlow.test.js
```

### 4. 真实 API 测试 (Real API Tests)

**目的**: 使用真实第三方服务进行测试
**特点**: 需要真实 API 密钥，慢但准确
**位置**: `test/integration/payment/*.real.test.js`

```bash
# 设置环境变量后运行
STRIPE_TEST_SECRET_KEY=sk_test_... npm test -- test/integration/payment/stripe.real.test.js
```

## 🚀 运行测试

### 基本命令

```bash
# 运行所有测试
npm test

# 运行特定测试文件
npm test -- test/unit/whatsapp/services/whatsappService.test.js

# 运行匹配模式的测试
npm test -- --testNamePattern="should create session"

# 以监视模式运行测试
npm test -- --watch

# 运行测试并生成覆盖率报告
npm run test:coverage
```

### 环境变量

```bash
# 基本测试环境
NODE_ENV=test

# 使用真实数据库
USE_REAL_DB=true

# Stripe 真实测试
STRIPE_TEST_SECRET_KEY=sk_test_...

# PayPal 真实测试
PAYPAL_CLIENT_ID=...
PAYPAL_CLIENT_SECRET=...

# 调试模式
DEBUG=true
```

### 测试脚本

在 `package.json` 中定义的测试脚本：

```json
{
  "scripts": {
    "test": "NODE_ENV=test jest --config test/config/jest.config.js",
    "test:unit": "npm test -- test/unit",
    "test:integration": "npm test -- test/integration",
    "test:e2e": "npm test -- test/e2e",
    "test:coverage": "npm test -- --coverage",
    "test:watch": "npm test -- --watch",
    "test:debug": "DEBUG=true npm test"
  }
}
```

## 📊 测试覆盖率

### 查看覆盖率

```bash
# 生成覆盖率报告
npm run test:coverage

# 在浏览器中查看详细报告
open coverage/lcov-report/index.html
```

### 覆盖率目标

- **语句覆盖率**: > 80%
- **分支覆盖率**: > 75%
- **函数覆盖率**: > 85%
- **行覆盖率**: > 80%

### 覆盖率配置

在 `jest.config.js` 中配置：

```javascript
module.exports = {
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 85,
      lines: 80,
      statements: 80
    }
  },
  collectCoverageFrom: [
    'whatsapp/**/*.js',
    'graphql/**/*.js',
    'routes/**/*.js',
    'services/**/*.js',
    '!**/node_modules/**',
    '!**/test/**'
  ]
};
```

## ✍️ 编写新测试

### 1. 选择测试类型

**单元测试** - 当你需要测试：
- 单个函数的逻辑
- 类的方法
- 工具函数
- 纯函数

**集成测试** - 当你需要测试：
- API 端点
- 数据库操作
- 服务之间的交互
- 第三方集成

**E2E 测试** - 当你需要测试：
- 完整的用户流程
- 跨多个服务的操作
- 业务场景

### 2. 使用测试工厂

```javascript
const { createOrder } = require('../factories/orderFactory');
const { createCustomer } = require('../factories/customerFactory');

// 创建测试数据
const customer = await createCustomer({
  email: '<EMAIL>',
  name: 'Test User'
});

const order = await createOrder({
  user: customer._id,
  orderAmount: 25.99
});
```

### 3. 测试模板

#### 单元测试模板

```javascript
/**
 * [模块名] Unit Tests
 * 测试 [模块描述]
 */

const moduleToTest = require('../../path/to/module');

describe('[模块名] Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('[功能组]', () => {
    test('should [期望行为]', () => {
      // Arrange
      const input = 'test input';
      
      // Act
      const result = moduleToTest.functionToTest(input);
      
      // Assert
      expect(result).toBe('expected output');
    });
  });
});
```

#### 集成测试模板

```javascript
/**
 * [API/服务名] Integration Tests
 * 测试 [API/服务描述]
 */

const request = require('supertest');
const app = require('../../app');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');

describe('[API/服务名] Integration Tests', () => {
  beforeAll(async () => {
    await connectTestDB();
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
  });

  describe('[端点/功能组]', () => {
    test('should [期望行为]', async () => {
      const response = await request(app)
        .post('/api/endpoint')
        .send({ data: 'test' })
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });
});
```

### 4. Mock 使用指南

#### 何时使用 Mock

**应该 Mock**:
- 外部 API 调用
- 文件系统操作
- 网络请求
- 时间相关函数
- 随机数生成

**不应该 Mock**:
- 数据库操作（集成测试中）
- 业务逻辑
- 内部模块（除非必要）

#### Mock 示例

```javascript
// Mock 外部服务
jest.mock('../../../services/emailService', () => ({
  sendEmail: jest.fn().mockResolvedValue({ success: true })
}));

// Mock 特定函数
const mockFunction = jest.fn().mockReturnValue('mocked result');

// Mock 时间
jest.useFakeTimers();
jest.setSystemTime(new Date('2023-01-01'));
```

## 🎯 最佳实践

### 1. 测试命名

```javascript
// ✅ 好的测试名称
test('should create order when valid data is provided', () => {});
test('should return 400 when email is missing', () => {});
test('should update order status from PENDING to ACCEPTED', () => {});

// ❌ 不好的测试名称
test('test order creation', () => {});
test('error case', () => {});
test('status update', () => {});
```

### 2. 测试结构 (AAA 模式)

```javascript
test('should calculate total price correctly', () => {
  // Arrange - 准备测试数据
  const items = [
    { price: 10.00, quantity: 2 },
    { price: 5.00, quantity: 1 }
  ];
  const taxRate = 0.08;

  // Act - 执行被测试的操作
  const total = calculateTotal(items, taxRate);

  // Assert - 验证结果
  expect(total).toBe(27.00); // (10*2 + 5*1) * 1.08
});
```

### 3. 数据清理

```javascript
describe('Order Tests', () => {
  beforeEach(async () => {
    // 每个测试前清理数据
    await clearTestDB();
  });

  afterEach(async () => {
    // 每个测试后清理 mock
    jest.clearAllMocks();
  });
});
```

### 4. 异步测试

```javascript
// ✅ 正确的异步测试
test('should create user asynchronously', async () => {
  const user = await createUser({ name: 'Test' });
  expect(user.name).toBe('Test');
});

// ❌ 错误的异步测试
test('should create user asynchronously', () => {
  createUser({ name: 'Test' }).then(user => {
    expect(user.name).toBe('Test'); // 这个断言可能不会执行
  });
});
```

### 5. 错误测试

```javascript
test('should throw error when invalid data is provided', async () => {
  await expect(createOrder({})).rejects.toThrow('Invalid order data');
});

test('should return 400 for invalid request', async () => {
  const response = await request(app)
    .post('/api/orders')
    .send({})
    .expect(400);

  expect(response.body.error).toBeDefined();
});
```

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接问题

```bash
# 检查 MongoDB 是否运行
docker ps | grep mongo

# 检查连接字符串
echo $MONGO_URL
```

#### 2. 测试超时

```javascript
// 增加特定测试的超时时间
test('long running test', async () => {
  // 测试代码
}, 60000); // 60 秒超时
```

#### 3. Mock 不工作

```javascript
// 确保在导入模块之前设置 mock
jest.mock('../service');
const service = require('../service');

// 清理 mock
beforeEach(() => {
  jest.clearAllMocks();
});
```

#### 4. 内存泄漏

```bash
# 使用 --detectLeaks 标志
npm test -- --detectLeaks

# 检查是否有未关闭的连接
npm test -- --detectOpenHandles
```

### 调试技巧

```javascript
// 1. 使用 console.log (在测试中)
test('debug test', () => {
  console.log('Debug info:', data);
  expect(data).toBeDefined();
});

// 2. 使用 debugger
test('debug test', () => {
  debugger; // 在 Node.js 调试器中暂停
  expect(result).toBe(expected);
});

// 3. 只运行特定测试
test.only('focus on this test', () => {
  // 只运行这个测试
});

// 4. 跳过测试
test.skip('skip this test', () => {
  // 跳过这个测试
});
```

## 📚 相关资源

- [Jest 官方文档](https://jestjs.io/docs/getting-started)
- [Supertest 文档](https://github.com/visionmedia/supertest)
- [Testcontainers 文档](https://www.testcontainers.org/)
- [测试最佳实践](https://github.com/goldbergyoni/javascript-testing-best-practices)

---

**注意**: 这个指南会随着项目的发展而更新。如果你发现任何问题或有改进建议，请创建 issue 或提交 PR。
