# 📊 测试覆盖率指南

本文档详细说明如何查看、分析和改进 Firespoon API 项目的测试覆盖率。

## 🎯 覆盖率目标

### 当前目标
- **语句覆盖率 (Statements)**: > 80%
- **分支覆盖率 (Branches)**: > 75%
- **函数覆盖率 (Functions)**: > 85%
- **行覆盖率 (Lines)**: > 80%

### 核心模块目标
- **WhatsApp 服务**: > 90%
- **订单管理**: > 85%
- **支付系统**: > 80%
- **GraphQL API**: > 75%

## 🚀 生成覆盖率报告

### 基本命令

```bash
# 生成完整覆盖率报告
npm run test:coverage

# 生成特定模块的覆盖率
npm test -- --coverage --testPathPattern=whatsapp

# 生成覆盖率并在浏览器中查看
npm run test:coverage && open coverage/lcov-report/index.html
```

### 高级选项

```bash
# 只收集特定文件的覆盖率
npm test -- --coverage --collectCoverageFrom="whatsapp/**/*.js"

# 设置覆盖率阈值
npm test -- --coverage --coverageThreshold='{"global":{"branches":80,"functions":90,"lines":85,"statements":85}}'

# 生成不同格式的报告
npm test -- --coverage --coverageReporters=text,lcov,html,json
```

## 📋 覆盖率配置

### Jest 配置 (jest.config.js)

```javascript
module.exports = {
  // 启用覆盖率收集
  collectCoverage: true,
  
  // 覆盖率输出目录
  coverageDirectory: 'coverage',
  
  // 覆盖率报告格式
  coverageReporters: [
    'text',        // 控制台输出
    'lcov',        // 详细的HTML报告
    'html',        // 简化的HTML报告
    'json',        // JSON格式
    'clover'       // Clover XML格式
  ],
  
  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 75,
      functions: 85,
      lines: 80,
      statements: 80
    },
    // 特定目录的阈值
    './whatsapp/': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  
  // 包含在覆盖率中的文件
  collectCoverageFrom: [
    'whatsapp/**/*.js',
    'graphql/**/*.js',
    'routes/**/*.js',
    'services/**/*.js',
    'models/**/*.js',
    '!**/node_modules/**',
    '!**/test/**',
    '!**/coverage/**',
    '!**/*.config.js',
    '!**/index.js'
  ],
  
  // 排除的文件模式
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/test/',
    '/coverage/',
    '/config/',
    'index.js',
    '.config.js'
  ]
};
```

### 环境变量配置

```bash
# .env.test
NODE_ENV=test
COLLECT_COVERAGE=true
COVERAGE_THRESHOLD_BRANCHES=75
COVERAGE_THRESHOLD_FUNCTIONS=85
COVERAGE_THRESHOLD_LINES=80
COVERAGE_THRESHOLD_STATEMENTS=80
```

## 📊 理解覆盖率报告

### 覆盖率类型说明

#### 1. 语句覆盖率 (Statement Coverage)
```javascript
// 示例代码
function calculateTotal(items) {
  let total = 0;           // 语句 1
  for (let item of items) { // 语句 2
    total += item.price;   // 语句 3
  }
  return total;           // 语句 4
}

// 测试用例
test('should calculate total', () => {
  const result = calculateTotal([{price: 10}, {price: 20}]);
  expect(result).toBe(30);
});
// 覆盖率: 4/4 = 100%
```

#### 2. 分支覆盖率 (Branch Coverage)
```javascript
// 示例代码
function applyDiscount(price, hasDiscount) {
  if (hasDiscount) {      // 分支 1: true
    return price * 0.9;   // 分支 1a
  } else {                // 分支 1: false
    return price;         // 分支 1b
  }
}

// 需要两个测试用例来达到100%分支覆盖率
test('should apply discount', () => {
  expect(applyDiscount(100, true)).toBe(90);   // 覆盖分支 1a
  expect(applyDiscount(100, false)).toBe(100); // 覆盖分支 1b
});
// 覆盖率: 2/2 = 100%
```

#### 3. 函数覆盖率 (Function Coverage)
```javascript
// 示例代码
class OrderService {
  createOrder() { /* ... */ }    // 函数 1
  updateOrder() { /* ... */ }    // 函数 2
  deleteOrder() { /* ... */ }    // 函数 3
}

// 测试用例
test('should create order', () => {
  const service = new OrderService();
  service.createOrder();  // 只调用了函数 1
});
// 覆盖率: 1/3 = 33%
```

#### 4. 行覆盖率 (Line Coverage)
类似于语句覆盖率，但以代码行为单位计算。

### HTML 报告解读

#### 1. 总览页面
- **绿色**: 覆盖率 >= 80%
- **黄色**: 覆盖率 50-79%
- **红色**: 覆盖率 < 50%

#### 2. 文件详情页面
- **绿色行**: 已覆盖的代码
- **红色行**: 未覆盖的代码
- **黄色行**: 部分覆盖的分支

#### 3. 分支指示器
```
E: 表示 else 分支
I: 表示 if 分支
数字: 表示执行次数
```

## 🔍 分析覆盖率

### 识别问题区域

#### 1. 低覆盖率文件
```bash
# 查找覆盖率低于70%的文件
npm test -- --coverage --coverageThreshold='{"global":{"statements":70}}'
```

#### 2. 未覆盖的分支
```javascript
// 示例：未覆盖的错误处理分支
function processPayment(amount) {
  if (amount <= 0) {
    throw new Error('Invalid amount'); // 这个分支可能未被测试
  }
  return processValidPayment(amount);
}

// 添加测试用例
test('should throw error for invalid amount', () => {
  expect(() => processPayment(-10)).toThrow('Invalid amount');
});
```

#### 3. 未覆盖的函数
```javascript
// 示例：工具函数未被测试
class DateUtils {
  static formatDate(date) { /* ... */ }     // 已测试
  static parseDate(string) { /* ... */ }    // 已测试
  static isWeekend(date) { /* ... */ }      // 未测试 ❌
}

// 添加测试
test('should check if date is weekend', () => {
  const saturday = new Date('2023-01-07'); // Saturday
  expect(DateUtils.isWeekend(saturday)).toBe(true);
});
```

### 提高覆盖率的策略

#### 1. 边界条件测试
```javascript
// 测试边界值
describe('calculateDiscount', () => {
  test('should handle zero amount', () => {
    expect(calculateDiscount(0, 10)).toBe(0);
  });
  
  test('should handle maximum discount', () => {
    expect(calculateDiscount(100, 100)).toBe(0);
  });
  
  test('should handle negative values', () => {
    expect(() => calculateDiscount(-10, 10)).toThrow();
  });
});
```

#### 2. 错误路径测试
```javascript
// 测试异常情况
test('should handle database connection error', async () => {
  // Mock 数据库错误
  jest.spyOn(database, 'connect').mockRejectedValue(new Error('Connection failed'));
  
  await expect(userService.createUser({})).rejects.toThrow('Connection failed');
});
```

#### 3. 异步代码测试
```javascript
// 测试 Promise 的 resolve 和 reject 路径
test('should handle async success', async () => {
  const result = await asyncFunction();
  expect(result).toBeDefined();
});

test('should handle async failure', async () => {
  jest.spyOn(externalService, 'call').mockRejectedValue(new Error('Service error'));
  
  await expect(asyncFunction()).rejects.toThrow('Service error');
});
```

## 🎯 覆盖率最佳实践

### 1. 质量优于数量
```javascript
// ❌ 为了覆盖率而写的无意义测试
test('should return user name', () => {
  const user = new User('John');
  expect(user.getName()).toBe('John'); // 简单的 getter，没有业务逻辑
});

// ✅ 有意义的业务逻辑测试
test('should calculate user discount based on membership level', () => {
  const premiumUser = new User('John', 'PREMIUM');
  const discount = premiumUser.calculateDiscount(100);
  expect(discount).toBe(20); // 测试实际的业务逻辑
});
```

### 2. 忽略不重要的代码
```javascript
// jest.config.js
module.exports = {
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/test/',
    '/config/',
    'index.js',           // 简单的入口文件
    '*.config.js',        // 配置文件
    '/migrations/',       // 数据库迁移文件
    '/seeds/'            // 数据库种子文件
  ]
};
```

### 3. 设置合理的阈值
```javascript
// 不同模块设置不同的阈值
coverageThreshold: {
  global: {
    branches: 75,
    functions: 85,
    lines: 80,
    statements: 80
  },
  // 核心业务逻辑要求更高覆盖率
  './services/orderService.js': {
    branches: 90,
    functions: 95,
    lines: 90,
    statements: 90
  },
  // 工具函数可以要求较低覆盖率
  './utils/': {
    branches: 70,
    functions: 80,
    lines: 75,
    statements: 75
  }
}
```

## 🔧 CI/CD 集成

### GitHub Actions 配置
```yaml
# .github/workflows/test.yml
name: Test Coverage

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests with coverage
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v2
      with:
        file: ./coverage/lcov.info
        
    - name: Comment coverage on PR
      uses: romeovs/lcov-reporter-action@v0.3.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        lcov-file: ./coverage/lcov.info
```

### 覆盖率徽章
```markdown
<!-- README.md -->
[![Coverage Status](https://codecov.io/gh/username/firespoon-api/branch/main/graph/badge.svg)](https://codecov.io/gh/username/firespoon-api)
```

## 📈 监控和维护

### 1. 定期审查
- 每周检查覆盖率趋势
- 识别覆盖率下降的模块
- 审查新增代码的测试覆盖率

### 2. 覆盖率报告
```bash
# 生成覆盖率趋势报告
npm run coverage:trend

# 比较两个版本的覆盖率
npm run coverage:diff main feature-branch
```

### 3. 自动化检查
```javascript
// package.json
{
  "scripts": {
    "test:coverage:check": "npm run test:coverage && node scripts/check-coverage.js",
    "precommit": "npm run test:coverage:check"
  }
}
```

## 🎯 总结

好的测试覆盖率应该：

1. **关注重要代码**: 优先覆盖核心业务逻辑
2. **测试边界条件**: 包括错误情况和边界值
3. **保持可维护性**: 测试代码应该易于理解和维护
4. **持续改进**: 定期审查和优化测试覆盖率

记住：**100% 的覆盖率不等于 100% 的质量**。重要的是测试的质量和对业务逻辑的覆盖程度。
