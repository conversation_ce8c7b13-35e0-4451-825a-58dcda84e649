TestFile: refund.integration.test.js
---
# 全额退款（取消订单）测试组
TestFunction: cancelOrderWithRefund
Cases:
  - CaseID: "R-CANCEL-001"
    Module: "refund"
    Description: "商家原因全额退款 - 缺货"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "smoke"
      - "positive"
      - "full-refund"
    Precondition:
      - "创建已支付的Stripe订单"
      - "设置餐厅管理员认证token"
      - "Mock Stripe退款API返回成功"
      - "启动MongoDB和Redis容器"
    Steps:
      - "调用cancelOrder mutation"
      - "传入orderId和reason: MERCHANT_OUT_OF_STOCK"
      - "验证退款记录创建"
      - "验证Stripe API调用"
      - "模拟Stripe webhook回调"
    ExpectedResult:
      - "订单状态变为CANCELLED"
      - "订单refundStatus变为FULL"
      - "订单totalRefunded等于orderAmount"
      - "退款记录状态为SUCCEEDED"
      - "客户收到全额退款"
      - "商家承担交易手续费"
      - "发送退款成功通知"

  - CaseID: "R-CANCEL-002"
    Module: "refund"
    Description: "客户原因全额退款"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "smoke"
      - "positive"
      - "full-refund"
    Precondition:
      - "创建已支付的Stripe订单"
      - "设置餐厅管理员认证token"
      - "Mock Stripe退款API返回成功"
    Steps:
      - "调用cancelOrder mutation"
      - "传入reason: CUSTOMER_CANCELLED"
      - "验证手续费计算逻辑"
    ExpectedResult:
      - "订单状态变为CANCELLED"
      - "订单refundStatus变为FULL"
      - "订单totalRefunded等于实际退款金额"
      - "客户承担按比例分摊的手续费"
      - "退款金额 = 订单金额 - 手续费"



  - CaseID: "R-CANCEL-003"
    Module: "refund"
    Description: "权限验证失败"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "security"
      - "negative"
    Precondition:
      - "创建已支付订单"
      - "使用其他餐厅的认证token"
    Steps:
      - "调用cancelOrder mutation"
      - "尝试取消其他餐厅的订单"
    ExpectedResult:
      - "返回403权限错误"
      - "订单状态不变"
      - "不创建退款记录"

---
# 部分退款测试组
TestFunction: partialRefund
Cases:
  - CaseID: "R-PARTIAL-001"
    Module: "refund"
    Description: "商家原因部分退款"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "smoke"
      - "positive"
      - "partial-refund"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "设置餐厅管理员认证token"
      - "Mock Stripe退款API返回成功"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 30.00, reason: MERCHANT_OTHER"
      - "传入reasonText: '部分商品缺货'"
      - "验证退款计算逻辑"
    ExpectedResult:
      - "订单状态变为PARTIALLY_REFUNDED"
      - "订单refundStatus变为PARTIAL"
      - "退款记录创建成功"
      - "客户收到30.00退款"
      - "商家承担手续费"
      - "订单totalRefunded更新为30.00"

  - CaseID: "R-PARTIAL-002"
    Module: "refund"
    Description: "客户原因部分退款"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "positive"
      - "partial-refund"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "设置餐厅管理员认证token"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 25.00, reason: CUSTOMER_CANCELLED"
    ExpectedResult:
      - "订单refundStatus变为PARTIAL"
      - "客户承担按比例手续费"
      - "实际退款金额 < 25.00"
      - "feeBearer设置为CUSTOMER"
      - "订单totalRefunded更新为实际退款金额"

  - CaseID: "R-PARTIAL-003"
    Module: "refund"
    Description: "多次部分退款"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "complex"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "已有一次部分退款（30.00）"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 20.00"
      - "验证可退款余额计算"
    ExpectedResult:
      - "订单refundStatus保持为PARTIAL"
      - "订单totalRefunded更新为50.00"
      - "可退款余额为50.00"
      - "创建第二条退款记录"

  - CaseID: "R-PARTIAL-004"
    Module: "refund"
    Description: "超额退款验证"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "已有部分退款（80.00）"
    Steps:
      - "调用refundOrder mutation"
      - "传入amount: 30.00（超过可退款余额）"
    ExpectedResult:
      - "返回验证错误"
      - "错误信息包含可退款余额"
      - "不创建退款记录"

---
# Stripe集成测试组
TestFunction: stripeIntegration
Cases:
  - CaseID: "R-STRIPE-001"
    Module: "stripe"
    Description: "Stripe退款API调用成功"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "stripe"
    Precondition:
      - "配置Stripe测试环境"
      - "创建真实的Stripe支付意图"
      - "订单关联Stripe支付ID"
    Steps:
      - "发起部分退款请求"
      - "调用Stripe退款API"
      - "验证Stripe响应"
    ExpectedResult:
      - "Stripe返回退款对象"
      - "退款状态为pending或succeeded"
      - "退款记录保存stripeRefundId"

  - CaseID: "R-STRIPE-002"
    Module: "stripe"
    Description: "Stripe退款API调用失败"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "negative"
      - "stripe"
    Precondition:
      - "Mock Stripe API返回错误"
      - "创建已支付订单"
    Steps:
      - "发起退款请求"
      - "Stripe API返回错误"
      - "验证错误处理"
    ExpectedResult:
      - "退款记录状态为FAILED"
      - "记录错误信息"
      - "不更新订单状态"

  - CaseID: "R-STRIPE-003"
    Module: "stripe"
    Description: "Webhook事件处理"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "webhook"
      - "stripe"
    Precondition:
      - "创建处理中的退款记录"
      - "配置Webhook签名验证"
    Steps:
      - "发送refund.updated webhook"
      - "传入status: succeeded"
      - "验证签名"
      - "处理webhook事件"
    ExpectedResult:
      - "退款记录状态更新为SUCCEEDED"
      - "订单状态相应更新"
      - "发送成功通知"

---
# 查询接口测试组
TestFunction: refundQueries
Cases:
  - CaseID: "R-QUERY-001"
    Module: "query"
    Description: "查询退款记录"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
    Precondition:
      - "创建多条退款记录"
      - "设置餐厅管理员认证"
    Steps:
      - "调用getRefund query"
      - "传入refundId"
      - "验证权限检查"
    ExpectedResult:
      - "返回完整退款信息"
      - "包含关联订单信息"
      - "只能查询自己餐厅的退款"

  - CaseID: "R-QUERY-002"
    Module: "query"
    Description: "查询订单退款历史"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "query"
    Precondition:
      - "创建有多次退款的订单"
    Steps:
      - "调用getOrderRefunds query"
      - "传入orderId"
    ExpectedResult:
      - "返回所有退款记录"
      - "按时间倒序排列"
      - "包含退款状态和金额"

---
# 错误处理和边界测试组
TestFunction: errorHandling
Cases:
  - CaseID: "R-ERROR-001"
    Module: "validation"
    Description: "无效退款金额"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "negative"
      - "validation"
    Precondition:
      - "创建已支付订单"
    Steps:
      - "调用refundOrder mutation"
      - "传入负数金额或零"
    ExpectedResult:
      - "返回验证错误"
      - "不创建退款记录"

  - CaseID: "R-ERROR-002"
    Module: "concurrency"
    Description: "并发退款请求"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "concurrency"
    Precondition:
      - "创建已支付订单"
    Steps:
      - "同时发起多个退款请求"
      - "验证并发控制"
    ExpectedResult:
      - "只有一个退款请求成功"
      - "其他请求返回冲突错误"

  - CaseID: "R-ERROR-003"
    Module: "network"
    Description: "网络超时处理"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "timeout"
    Precondition:
      - "Mock Stripe API超时"
    Steps:
      - "发起退款请求"
      - "等待超时"
    ExpectedResult:
      - "退款记录状态为FAILED"
      - "记录超时错误信息"

---
# 通知系统测试组
TestFunction: notifications
Cases:
  - CaseID: "R-NOTIFY-001"
    Module: "notification"
    Description: "退款成功通知"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "positive"
      - "notification"
    Precondition:
      - "配置通知服务"
      - "创建退款成功记录"
    Steps:
      - "触发退款成功事件"
      - "验证通知发送"
    ExpectedResult:
      - "发送客户通知"
      - "包含实际退款金额"
      - "包含到账时间说明"

  - CaseID: "R-NOTIFY-002"
    Module: "notification"
    Description: "退款失败通知"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "negative"
      - "notification"
    Precondition:
      - "创建退款失败记录"
    Steps:
      - "触发退款失败事件"
    ExpectedResult:
      - "发送失败通知"
      - "包含失败原因"
      - "提供后续处理建议"

---
# refundStatus字段专项测试组
TestFunction: refundStatusField
Cases:
  - CaseID: "R-STATUS-001"
    Module: "model"
    Description: "订单创建时refundStatus默认值验证"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "unit"
      - "model"
      - "positive"
    Precondition:
      - "连接测试数据库"
      - "清理测试数据"
    Steps:
      - "创建新订单实例（不设置refundStatus）"
      - "验证refundStatus默认值"
      - "验证totalRefunded默认值"
      - "验证refunds数组默认值"
    ExpectedResult:
      - "refundStatus默认为'NONE'"
      - "totalRefunded默认为0"
      - "refunds默认为空数组[]"

  - CaseID: "R-STATUS-002"
    Module: "model"
    Description: "refundStatus枚举值验证"
    Importance: "High"
    Status: "Implemented"
    Tags:
      - "unit"
      - "model"
      - "validation"
    Precondition:
      - "创建订单实例"
    Steps:
      - "设置refundStatus为'NONE'"
      - "设置refundStatus为'PARTIAL'"
      - "设置refundStatus为'FULL'"
      - "尝试设置无效值"
    ExpectedResult:
      - "有效枚举值设置成功"
      - "无效值抛出验证错误"
      - "枚举值包含['NONE', 'PARTIAL', 'FULL']"

  - CaseID: "R-STATUS-003"
    Module: "business"
    Description: "部分退款后refundStatus状态更新"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "business-logic"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "订单refundStatus为'NONE'"
    Steps:
      - "执行部分退款（30.00）"
      - "验证refundStatus更新"
      - "验证totalRefunded更新"
    ExpectedResult:
      - "refundStatus变为'PARTIAL'"
      - "totalRefunded变为30.00"
      - "订单状态保持或变为PARTIALLY_REFUNDED"

  - CaseID: "R-STATUS-004"
    Module: "business"
    Description: "全额退款后refundStatus状态更新"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "business-logic"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "订单refundStatus为'NONE'"
    Steps:
      - "执行全额退款（100.00）"
      - "验证refundStatus更新"
      - "验证totalRefunded更新"
    ExpectedResult:
      - "refundStatus变为'FULL'"
      - "totalRefunded变为100.00"
      - "订单状态变为CANCELLED"

  - CaseID: "R-STATUS-005"
    Module: "business"
    Description: "多次部分退款达到全额时状态更新"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "integration"
      - "complex"
    Precondition:
      - "创建已支付订单（金额100.00）"
      - "已有部分退款（60.00），refundStatus为'PARTIAL'"
    Steps:
      - "执行第二次部分退款（40.00）"
      - "验证refundStatus状态变化"
      - "验证totalRefunded累计"
    ExpectedResult:
      - "refundStatus从'PARTIAL'变为'FULL'"
      - "totalRefunded变为100.00"
      - "订单状态相应更新"

  - CaseID: "R-STATUS-006"
    Module: "graphql"
    Description: "GraphQL查询refundStatus字段"
    Importance: "Medium"
    Status: "Planned"
    Tags:
      - "integration"
      - "graphql"
    Precondition:
      - "创建有不同refundStatus的订单"
    Steps:
      - "查询订单的refundStatus字段"
      - "验证返回的枚举值"
      - "验证类型安全性"
    ExpectedResult:
      - "正确返回OrderRefundStatus枚举值"
      - "支持NONE、PARTIAL、FULL查询"
      - "类型检查通过"

  - CaseID: "R-STATUS-007"
    Module: "data-consistency"
    Description: "refundStatus与totalRefunded数据一致性"
    Importance: "High"
    Status: "Planned"
    Tags:
      - "integration"
      - "data-consistency"
    Precondition:
      - "创建多个不同状态的订单"
    Steps:
      - "验证refundStatus='NONE'时totalRefunded=0"
      - "验证refundStatus='PARTIAL'时0<totalRefunded<orderAmount"
      - "验证refundStatus='FULL'时totalRefunded>=orderAmount"
    ExpectedResult:
      - "所有订单的refundStatus与totalRefunded保持一致"
      - "不存在数据不一致的情况"
      - "业务逻辑正确执行"
